'use client';

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';

const ProfilePage = () => {
  // In a real app, you would fetch user data here
  const userData = {
    full_name: '<PERSON>',
    address: '123 Flower Street, Da Lat',
    birth_date: '1990-01-01',
    sex: 'Male',
    points: 100,
    is_seller: false,
    created_date: '2023-01-01',
    email: '<EMAIL>',
    phone: '+84 123 456 789'
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 to-pink-50 p-4">
      <div className="max-w-4xl mx-auto">
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-800 mb-2">My Profile</h1>
          <p className="text-gray-600">Manage your personal information and preferences</p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Profile Summary Card */}
          <div className="lg:col-span-1">
            <Card className="text-center">
              <CardHeader>
                <div className="w-24 h-24 mx-auto bg-gradient-to-br from-purple-400 to-pink-400 rounded-full flex items-center justify-center text-white text-3xl font-bold mb-4">
                  {userData.full_name.charAt(0)}
                </div>
                <CardTitle className="text-xl">{userData.full_name}</CardTitle>
                <CardDescription>
                  Member since {new Date(userData.created_date).toLocaleDateString()}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <Badge variant="secondary" className="text-sm px-3 py-1">
                    {userData.points} Points
                  </Badge>
                  <Separator />
                  <Badge variant={userData.is_seller ? "default" : "outline"}>
                    {userData.is_seller ? 'Seller' : 'Customer'}
                  </Badge>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Profile Details Card */}
          <div className="lg:col-span-2">
            <Card>
              <CardHeader>
                <CardTitle>Personal Information</CardTitle>
                <CardDescription>
                  Your account details and personal information
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium text-gray-700">Full Name</label>
                    <p className="mt-1 text-gray-900">{userData.full_name}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-700">Gender</label>
                    <p className="mt-1 text-gray-900">{userData.sex}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-700">Birth Date</label>
                    <p className="mt-1 text-gray-900">
                      {new Date(userData.birth_date).toLocaleDateString()}
                    </p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-700">Email</label>
                    <p className="mt-1 text-gray-900">{userData.email}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-700">Phone</label>
                    <p className="mt-1 text-gray-900">{userData.phone}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-700">Account Type</label>
                    <p className="mt-1 text-gray-900">
                      {userData.is_seller ? 'Seller Account' : 'Customer Account'}
                    </p>
                  </div>
                </div>
                
                <Separator />
                
                <div>
                  <label className="text-sm font-medium text-gray-700">Address</label>
                  <p className="mt-1 text-gray-900">{userData.address}</p>
                </div>

                <div className="flex flex-wrap gap-3 pt-4">
                  <Button variant="default">
                    Edit Profile
                  </Button>
                  <Button variant="outline">
                    Change Password
                  </Button>
                  <Button variant="outline">
                    Account Settings
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="mt-8">
          <Card>
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
              <CardDescription>
                Common tasks and shortcuts
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <Button variant="outline" className="h-20 flex flex-col gap-2">
                  <span className="text-2xl">📦</span>
                  <span className="text-sm">My Orders</span>
                </Button>
                <Button variant="outline" className="h-20 flex flex-col gap-2">
                  <span className="text-2xl">🎫</span>
                  <span className="text-sm">Vouchers</span>
                </Button>
                <Button variant="outline" className="h-20 flex flex-col gap-2">
                  <span className="text-2xl">📍</span>
                  <span className="text-sm">Addresses</span>
                </Button>
                <Button variant="outline" className="h-20 flex flex-col gap-2">
                  <span className="text-2xl">💳</span>
                  <span className="text-sm">Payment</span>
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default ProfilePage;
